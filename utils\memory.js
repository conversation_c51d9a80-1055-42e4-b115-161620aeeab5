// Memory management module for Discord AI Bot
// Handles user conversation history and context building

// Memory storage for each user's conversation history
const userMemories = new Map();

/**
 * Retrieves or creates a user's conversation memory
 * @param {string} userId - Discord user ID
 * @returns {Array} - User's conversation history array
 */
function getUserMemory(userId) {
  if (!userMemories.has(userId)) {
    userMemories.set(userId, []);
  }
  return userMemories.get(userId);
}

/**
 * Adds a new conversation to user's memory with interaction flags
 * @param {string} userId - Discord user ID
 * @param {string} userMessage - User's message content
 * @param {string} botResponse - Bot's response content
 * @param {boolean} hasImage - Whether interaction included an image
 * @param {boolean} hasSearch - Whether interaction included a web search
 */
function addToMemory(userId, userMessage, botResponse, hasImage = false, hasSearch = false) {
  const memory = getUserMemory(userId);

  // Add new conversation to memory
  memory.push({
    user: userMessage,
    bot: botResponse,
    hasImage: hasImage,
    hasSearch: hasSearch,
    timestamp: Date.now()
  });

  // Keep only last 10 conversations to prevent memory overflow
  if (memory.length > 10) {
    memory.shift();
  }
}

/**
 * Builds conversation context string from user's memory for AI prompts
 * @param {string} userId - Discord user ID
 * @returns {string} - Formatted conversation context
 */
function buildConversationContext(userId) {
  const memory = getUserMemory(userId);
  let context = '';

  // Add previous conversations to context with interaction indicators
  memory.forEach(conversation => {
    let userMessage = conversation.user;
    
    // Add indicators for special interactions
    if (conversation.hasImage) {
      userMessage += ' [User shared an image]';
    }
    if (conversation.hasSearch) {
      userMessage += ' [Bot performed web search]';
    }
    
    context += `User: ${userMessage}\nBot: ${conversation.bot}\n\n`;
  });

  return context;
}

module.exports = {
  getUserMemory,
  addToMemory,
  buildConversationContext
};
