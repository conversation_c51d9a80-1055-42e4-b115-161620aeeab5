# Discord AI Chatbot Tutorial

A simple Discord bot that uses Google Gemini AI to respond when mentioned or in direct messages.

## How the Bot Works

The bot will respond in two situations:
1. **When mentioned** - Type `@YourBotName hello` in any channel
2. **Direct messages** - Send a private message to the bot

## Setup Guide

### 1. Install Dependencies
```bash
npm install
```

### 2. Create Environment File
Copy `.env.example` to `.env` and fill in your values:
```
DISCORD_BOT_TOKEN=your_discord_bot_token_here
GEMINI_API_KEY=your_gemini_api_key_here
```

### 3. Get Discord Bot Token
1. Go to https://discord.com/developers/applications
2. Click "New Application" and give it a name
3. Go to "Bot" section in the left sidebar
4. Click "Add Bot"
5. Copy the token under "Token" section
6. Paste it in your `.env` file

### 4. Get Gemini API Key
1. Go to https://makersuite.google.com/app/apikey
2. Click "Create API Key"
3. Copy the generated key
4. Paste it in your `.env` file

### 5. Invite <PERSON> to Your Server
1. In Discord Developer Portal, go to "OAuth2" > "URL Generator"
2. Select scopes: `bot`
3. Select bot permissions: `Send Messages`, `Read Messages`, `Read Message History`
4. Copy the generated URL and open it in browser
5. Select your server and authorize

### 6. Run the Bot
```bash
node index.js
```

## Usage Examples

**In any channel (mention the bot):**
- `@YourBotName what's the weather like?`
- `@YourBotName tell me a joke`

**Direct message:**
- Just send any message directly to the bot

## Features

- Responds to mentions in any channel
- Uses Google's Gemini Pro AI model with custom training
- Custom personality and rules system
- Conversation memory for each user (remembers last 10 conversations)
- Real-time web search for current information
- Automatic keyword detection for search triggers
- Simple and educational code structure

## Training System

The bot uses a training system to define its personality:

- **training.json** - Contains bot personality, rules, and behavior
- **training.js** - Loads and formats the training data
- **index.js** - Uses the training to create system prompts

### Customizing Your Bot

Edit `data.json` to change:
- Bot name and creator
- System prompt with personality and rules

## Web Search System

The bot automatically detects when users need current information and performs web searches:

### Search Triggers
- Keywords: "latest", "current", "today", "news about", "weather in", "stock price"
- Question patterns: "what's happening", "how much cost", "when release"

### How It Works
1. Bot analyzes user message for search keywords
2. Extracts search query from the message
3. Performs web search using DuckDuckGo API
4. Integrates results into AI response naturally
5. Falls back to normal AI response if search fails

### Example Usage
- `@Bot what's the latest news about AI?`
- `@Bot what's the weather like today?`
- `@Bot how much does Bitcoin cost now?`
