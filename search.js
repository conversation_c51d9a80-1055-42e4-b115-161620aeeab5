// Search module for Discord AI Bot using Serper.dev
// This module provides web search functionality with automatic keyword detection

const axios = require('axios');

/**
 * Performs a web search using Serper.dev API
 * @param {string} query - The search query
 * @returns {Promise<Object>} - Search results object
 */
async function performWebSearch(query) {
  try {
    // Prepare the search request data
    const searchData = JSON.stringify({
      "q": query,
      "num": 5 // Limit to 5 results for better response time
    });

    // Configure the API request
    const config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: 'https://google.serper.dev/search',
      headers: { 
        'X-API-KEY': process.env.SERPER_API_KEY,
        'Content-Type': 'application/json'
      },
      data: searchData
    };

    // Make the search request
    const response = await axios.request(config);
    
    // Return the search results
    return {
      success: true,
      data: response.data,
      query: query
    };

  } catch (error) {
    console.error('Search error:', error.message);
    return {
      success: false,
      error: error.message,
      query: query
    };
  }
}

/**
 * Formats search results into a readable text format
 * @param {Object} searchResults - Raw search results from Serper
 * @returns {string} - Formatted search results text
 */
function formatSearchResults(searchResults) {
  if (!searchResults.success) {
    return `Search failed: ${searchResults.error}`;
  }

  const data = searchResults.data;
  let formattedResults = `Search results for "${searchResults.query}":\n\n`;

  // Add organic search results
  if (data.organic && data.organic.length > 0) {
    data.organic.slice(0, 5).forEach((result, index) => {
      formattedResults += `${index + 1}. **${result.title}**\n`;
      formattedResults += `   ${result.snippet}\n`;
      formattedResults += `   Source: ${result.link}\n\n`;
    });
  }

  // Add knowledge graph if available
  if (data.knowledgeGraph) {
    const kg = data.knowledgeGraph;
    formattedResults += `**Quick Info:**\n`;
    if (kg.title) formattedResults += `${kg.title}\n`;
    if (kg.description) formattedResults += `${kg.description}\n`;
    if (kg.attributes) {
      Object.entries(kg.attributes).slice(0, 3).forEach(([key, value]) => {
        formattedResults += `${key}: ${value}\n`;
      });
    }
    formattedResults += '\n';
  }

  return formattedResults;
}

/**
 * Detects if a message contains search intent keywords
 * @param {string} message - The user message to analyze
 * @returns {boolean} - True if search intent is detected
 */
function detectSearchIntent(message) {
  // Convert to lowercase for case-insensitive matching
  const lowerMessage = message.toLowerCase();
  
  // Search trigger keywords and phrases
  const searchKeywords = [
    'search for',
    'look up',
    'find information about',
    'what is',
    'who is',
    'when did',
    'where is',
    'how to',
    'tell me about',
    'information on',
    'details about',
    'search',
    'google',
    'find',
    'lookup'
  ];

  // Check if any search keywords are present
  return searchKeywords.some(keyword => lowerMessage.includes(keyword));
}

/**
 * Extracts search query from user message
 * @param {string} message - The user message
 * @returns {string} - Extracted search query
 */
function extractSearchQuery(message) {
  // Remove bot mentions and common search prefixes
  let query = message
    .replace(/<@!?\d+>/g, '') // Remove Discord mentions
    .replace(/search for/gi, '')
    .replace(/look up/gi, '')
    .replace(/find information about/gi, '')
    .replace(/tell me about/gi, '')
    .replace(/information on/gi, '')
    .replace(/details about/gi, '')
    .replace(/what is/gi, '')
    .replace(/who is/gi, '')
    .replace(/when did/gi, '')
    .replace(/where is/gi, '')
    .replace(/how to/gi, '')
    .replace(/search/gi, '')
    .replace(/google/gi, '')
    .replace(/find/gi, '')
    .replace(/lookup/gi, '')
    .trim();

  // If query is empty or too short, use the original message
  if (query.length < 3) {
    query = message.replace(/<@!?\d+>/g, '').trim();
  }

  return query;
}

/**
 * Performs search and returns formatted results
 * @param {string} userMessage - The original user message
 * @returns {Promise<Object>} - Object containing search results and metadata
 */
async function searchAndFormat(userMessage) {
  const query = extractSearchQuery(userMessage);
  const searchResults = await performWebSearch(query);
  const formattedResults = formatSearchResults(searchResults);
  
  return {
    query: query,
    success: searchResults.success,
    formattedResults: formattedResults,
    rawData: searchResults.data
  };
}

module.exports = {
  performWebSearch,
  formatSearchResults,
  detectSearchIntent,
  extractSearchQuery,
  searchAndFormat
};
