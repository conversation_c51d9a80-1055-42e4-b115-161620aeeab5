// Image processing module for Discord AI Bot
// Handles image download, conversion, and validation for Gemini AI

const axios = require('axios');

/**
 * Downloads an image from URL and converts it to base64 format for Gemini AI
 * @param {string} url - Image URL from Discord attachment
 * @returns {Promise<Object>} - Base64 image data object for Gemini
 */
async function downloadImageAsBase64(url) {
  try {
    const response = await axios.get(url, {
      responseType: 'arraybuffer'
    });
    
    // Convert to base64
    const base64 = Buffer.from(response.data, 'binary').toString('base64');
    
    // Get mime type from response headers
    const mimeType = response.headers['content-type'] || 'image/jpeg';
    
    return {
      inlineData: {
        data: base64,
        mimeType: mimeType
      }
    };
  } catch (error) {
    console.error('Error downloading image:', error);
    throw new Error('Failed to download image');
  }
}

/**
 * Checks if a Discord attachment is a supported image format
 * @param {Object} attachment - Discord attachment object
 * @returns {boolean} - True if image format is supported by Gemini
 */
function isSupportedImage(attachment) {
  const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  return supportedTypes.includes(attachment.contentType);
}

module.exports = {
  downloadImageAsBase64,
  isSupportedImage
};
