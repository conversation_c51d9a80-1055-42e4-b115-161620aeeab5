// AI response generation module for Discord AI Bot
// Handles different types of AI responses using Gemini AI

const { buildConversationContext } = require('./memory');

/**
 * Generates AI response for text-only messages with conversation context
 * @param {string} userMessage - User's text message
 * @param {string} userId - Discord user ID for memory context
 * @param {Object} model - Gemini AI model instance
 * @param {string} systemPrompt - <PERSON><PERSON>'s personality and training prompt
 * @returns {Promise<string>} - AI generated response
 */
async function getAIResponse(userMessage, userId, model, systemPrompt) {
  const conversationContext = buildConversationContext(userId);
  const fullPrompt = systemPrompt + '\n\nConversation History:\n' + conversationContext + 'User: ' + userMessage + '\n\nResponse:';

  const result = await model.generateContent(fullPrompt);
  const response = await result.response;
  return response.text();
}

/**
 * Generates AI response for messages with image attachments
 * @param {string} userMessage - User's text message (can be empty)
 * @param {Object} imageData - Base64 image data from imageProcessor
 * @param {string} userId - Discord user ID for memory context
 * @param {Object} model - Gemini AI model instance
 * @param {string} systemPrompt - Bot's personality and training prompt
 * @returns {Promise<string>} - AI generated response analyzing the image
 */
async function getAIResponseWithImage(userMessage, imageData, userId, model, systemPrompt) {
  const conversationContext = buildConversationContext(userId);
  const fullPrompt = systemPrompt + '\n\nConversation History:\n' + conversationContext + 'User: ' + userMessage + '\n\nResponse:';

  // Create content array with text and image for Gemini vision
  const content = [
    fullPrompt,
    imageData
  ];

  const result = await model.generateContent(content);
  const response = await result.response;
  return response.text();
}

/**
 * Generates AI response enhanced with web search results
 * @param {string} userMessage - User's search request message
 * @param {Object} searchResults - Formatted search results from search module
 * @param {string} userId - Discord user ID for memory context
 * @param {Object} model - Gemini AI model instance
 * @param {string} systemPrompt - Bot's personality and training prompt
 * @returns {Promise<string>} - AI response integrating search information
 */
async function getAIResponseWithSearch(userMessage, searchResults, userId, model, systemPrompt) {
  const conversationContext = buildConversationContext(userId);
  
  // Create enhanced prompt with search results
  const searchContext = `\n\nWeb Search Results:\n${searchResults.formattedResults}\n`;
  const fullPrompt = systemPrompt + '\n\nConversation History:\n' + conversationContext + 
                    'User: ' + userMessage + searchContext + 
                    '\n\nPlease provide a comprehensive response using the search results above. ' +
                    'Integrate the information naturally without explicitly mentioning that you performed a search.\n\nResponse:';

  const result = await model.generateContent(fullPrompt);
  const response = await result.response;
  return response.text();
}

module.exports = {
  getAIResponse,
  getAIResponseWithImage,
  getAIResponseWithSearch
};
