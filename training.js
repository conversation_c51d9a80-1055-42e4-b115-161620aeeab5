// Training module for Discord AI Bot
// This file loads the bot training data

const fs = require('fs');
const path = require('path');

// Load training data from JSON file
function loadTrainingData() {
  const dataPath = path.join(__dirname, 'data.json');
  const rawData = fs.readFileSync(dataPath, 'utf8');
  return JSON.parse(rawData);
}

// Get system prompt
function getSystemPrompt() {
  const data = loadTrainingData();
  return data.system_prompt;
}

// Get bot name
function getBotName() {
  const data = loadTrainingData();
  return data.name;
}

module.exports = {
  getSystemPrompt,
  getBotName,
  loadTrainingData
};
