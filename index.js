// Discord AI Chatbot with Training and Memory
// This bot uses Google Gemini AI with custom personality training and conversation memory

// Import packages
const { Client, GatewayIntentBits } = require('discord.js');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const dotenv = require('dotenv');
const training = require('./training.js');

// Load environment variables
dotenv.config();

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

// Load bot training data
const systemPrompt = training.getSystemPrompt();
const botName = training.getBotName();

// Memory storage for each user's conversation history
const userMemories = new Map();

// Create Discord client
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
  ],
});

// Function to get user's conversation history
function getUserMemory(userId) {
  if (!userMemories.has(userId)) {
    userMemories.set(userId, []);
  }
  return userMemories.get(userId);
}

// Function to add message to user's memory
function addToMemory(userId, userMessage, botResponse) {
  const memory = getUserMemory(userId);

  // Add new conversation to memory
  memory.push({
    user: userMessage,
    bot: botResponse,
    timestamp: Date.now()
  });

  // Keep only last 10 conversations to prevent memory overflow
  if (memory.length > 10) {
    memory.shift();
  }
}

// Function to build conversation context
function buildConversationContext(userId) {
  const memory = getUserMemory(userId);
  let context = '';

  // Add previous conversations to context
  memory.forEach(conversation => {
    context += `User: ${conversation.user}\nBot: ${conversation.bot}\n\n`;
  });

  return context;
}

// Function to get AI response with training and memory
async function getAIResponse(userMessage, userId) {
  const conversationContext = buildConversationContext(userId);
  const fullPrompt = systemPrompt + '\n\nConversation History:\n' + conversationContext + 'User: ' + userMessage + '\n\nResponse:';

  const result = await model.generateContent(fullPrompt);
  const response = await result.response;
  return response.text();
}

// Bot ready event
client.once('ready', () => {
  console.log(`${botName} is online!`);
});

// Message event
client.on('messageCreate', async (message) => {
  // Ignore bot messages
  if (message.author.bot) return;

  // Only respond if bot is mentioned
  if (!message.mentions.has(client.user)) return;

  // Get AI response with memory context
  const aiResponse = await getAIResponse(message.content, message.author.id);

  // Add this conversation to memory
  addToMemory(message.author.id, message.content, aiResponse);

  // Reply to the message
  await message.reply(aiResponse);
});
