// Discord AI Chatbot with Training, Memory, Image Analysis, and Web Search
// This bot uses Google Gemini AI with modular utility functions for clean organization

// Import packages
const { Client, GatewayIntentBits } = require('discord.js');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const dotenv = require('dotenv');

// Import utility modules
const training = require('./training.js');
const memory = require('./utils/memory.js');
const imageProcessor = require('./utils/imageProcessor.js');
const aiResponses = require('./utils/aiResponses.js');
const search = require('./utils/search.js');

// Load environment variables
dotenv.config();

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

// Load bot training data
const systemPrompt = training.getSystemPrompt();
const botName = training.getBotName();

// Create Discord client
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
  ],
});

// Bot ready event
client.once('ready', () => {
  console.log(`${botName} is online!`);
});

// Message event handler - orchestrates all bot functionality
client.on('messageCreate', async (message) => {
  // Ignore bot messages and only respond when mentioned
  if (message.author.bot || !message.mentions.has(client.user)) return;

  try {
    let aiResponse;
    let hasImage = false;
    let hasSearch = false;

    // Check for image attachments
    const imageAttachments = message.attachments.filter(attachment =>
      imageProcessor.isSupportedImage(attachment)
    );

    // Check for search intent
    const needsSearch = search.detectSearchIntent(message.content);

    if (imageAttachments.size > 0) {
      // Handle image analysis
      hasImage = true;
      const firstImage = imageAttachments.first();
      const imageData = await imageProcessor.downloadImageAsBase64(firstImage.url);
      const messageText = message.content || "What do you see in this image?";

      aiResponse = await aiResponses.getAIResponseWithImage(
        messageText, imageData, message.author.id, model, systemPrompt
      );

      console.log(`${botName} analyzed an image from ${message.author.username}`);

    } else if (needsSearch) {
      // Handle web search
      hasSearch = true;
      console.log(`${botName} performing search for ${message.author.username}: "${message.content}"`);

      const searchResults = await search.searchAndFormat(message.content);

      if (searchResults.success) {
        aiResponse = await aiResponses.getAIResponseWithSearch(
          message.content, searchResults, message.author.id, model, systemPrompt
        );
        console.log(`${botName} completed search for: "${searchResults.query}"`);
      } else {
        aiResponse = await aiResponses.getAIResponse(
          message.content, message.author.id, model, systemPrompt
        );
        console.log(`${botName} search failed, using regular response`);
        hasSearch = false;
      }

    } else {
      // Handle regular conversation
      aiResponse = await aiResponses.getAIResponse(
        message.content, message.author.id, model, systemPrompt
      );
    }

    // Store conversation in memory and reply
    memory.addToMemory(message.author.id, message.content, aiResponse, hasImage, hasSearch);
    await message.reply(aiResponse);

  } catch (error) {
    console.error('Error processing message:', error);
    await message.reply('Sorry, I encountered an error processing your message. Please try again.');
  }
});

// Login to Discord
client.login(process.env.DISCORD_BOT_TOKEN);
