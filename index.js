// Discord AI Chatbot with Training and Memory
// This bot uses Google Gemini AI with custom personality training and conversation memory

// Import packages
const { Client, GatewayIntentBits } = require('discord.js');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const dotenv = require('dotenv');
const training = require('./training.js');
const axios = require('axios');

// Load environment variables
dotenv.config();

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

// Load bot training data
const systemPrompt = training.getSystemPrompt();
const botName = training.getBotName();

// Memory storage for each user's conversation history
const userMemories = new Map();

// Create Discord client
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
  ],
});

// Function to get user's conversation history
function getUserMemory(userId) {
  if (!userMemories.has(userId)) {
    userMemories.set(userId, []);
  }
  return userMemories.get(userId);
}

// Function to add message to user's memory
function addToMemory(userId, userMessage, botResponse, hasImage = false) {
  const memory = getUserMemory(userId);

  // Add new conversation to memory
  memory.push({
    user: userMessage,
    bot: botResponse,
    hasImage: hasImage,
    timestamp: Date.now()
  });

  // Keep only last 10 conversations to prevent memory overflow
  if (memory.length > 10) {
    memory.shift();
  }
}

// Function to build conversation context
function buildConversationContext(userId) {
  const memory = getUserMemory(userId);
  let context = '';

  // Add previous conversations to context
  memory.forEach(conversation => {
    const userMessage = conversation.hasImage ? `${conversation.user} [User shared an image]` : conversation.user;
    context += `User: ${userMessage}\nBot: ${conversation.bot}\n\n`;
  });

  return context;
}

// Function to download and convert image to base64
async function downloadImageAsBase64(url) {
  try {
    const response = await axios.get(url, {
      responseType: 'arraybuffer'
    });

    // Convert to base64
    const base64 = Buffer.from(response.data, 'binary').toString('base64');

    // Get mime type from response headers
    const mimeType = response.headers['content-type'] || 'image/jpeg';

    return {
      inlineData: {
        data: base64,
        mimeType: mimeType
      }
    };
  } catch (error) {
    console.error('Error downloading image:', error);
    throw new Error('Failed to download image');
  }
}

// Function to check if attachment is a supported image
function isSupportedImage(attachment) {
  const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  return supportedTypes.includes(attachment.contentType);
}

// Function to get AI response with training and memory (text only)
async function getAIResponse(userMessage, userId) {
  const conversationContext = buildConversationContext(userId);
  const fullPrompt = systemPrompt + '\n\nConversation History:\n' + conversationContext + 'User: ' + userMessage + '\n\nResponse:';

  const result = await model.generateContent(fullPrompt);
  const response = await result.response;
  return response.text();
}

// Function to get AI response with image and text
async function getAIResponseWithImage(userMessage, imageData, userId) {
  const conversationContext = buildConversationContext(userId);
  const fullPrompt = systemPrompt + '\n\nConversation History:\n' + conversationContext + 'User: ' + userMessage + '\n\nResponse:';

  // Create content array with text and image
  const content = [
    fullPrompt,
    imageData
  ];

  const result = await model.generateContent(content);
  const response = await result.response;
  return response.text();
}

// Bot ready event
client.once('ready', () => {
  console.log(`${botName} is online!`);
});

// Message event
client.on('messageCreate', async (message) => {
  // Ignore bot messages
  if (message.author.bot) return;

  // Only respond if bot is mentioned
  if (!message.mentions.has(client.user)) return;

  try {
    let aiResponse;
    let hasImage = false;

    // Check if message has image attachments
    const imageAttachments = message.attachments.filter(attachment =>
      isSupportedImage(attachment)
    );

    if (imageAttachments.size > 0) {
      // Handle message with image(s)
      hasImage = true;
      const firstImage = imageAttachments.first();

      // Download and convert image to base64
      const imageData = await downloadImageAsBase64(firstImage.url);

      // Get AI response with image
      const messageText = message.content || "What do you see in this image?";
      aiResponse = await getAIResponseWithImage(messageText, imageData, message.author.id);

      console.log(`${botName} analyzed an image from ${message.author.username}`);
    } else {
      // Handle text-only message
      aiResponse = await getAIResponse(message.content, message.author.id);
    }

    // Add this conversation to memory
    addToMemory(message.author.id, message.content, aiResponse, hasImage);

    // Reply to the message
    await message.reply(aiResponse);

  } catch (error) {
    console.error('Error processing message:', error);
    await message.reply('Sorry, I encountered an error processing your message. Please try again.');
  }
});

// Login to Discord
client.login(process.env.DISCORD_BOT_TOKEN);
